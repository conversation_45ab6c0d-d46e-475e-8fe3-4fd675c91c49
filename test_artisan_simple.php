<?php

require __DIR__.'/vendor/autoload.php';

try {
    $app = require_once __DIR__.'/bootstrap/app.php';
    echo "Bootstrap successful\n";
    
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    echo "Kernel created successfully\n";
    
    // 尝试运行 --version 命令
    $input = new Symfony\Component\Console\Input\ArrayInput(['--version' => true]);
    $output = new Symfony\Component\Console\Output\BufferedOutput();
    
    $status = $kernel->handle($input, $output);
    echo "Command executed with status: $status\n";
    echo "Output: " . $output->fetch() . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
