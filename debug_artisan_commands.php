<?php

require __DIR__.'/vendor/autoload.php';

try {
    $app = require_once __DIR__.'/bootstrap/app.php';
    echo "Bootstrap successful\n";
    
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    echo "Kernel created successfully\n";
    
    // 尝试获取所有命令
    echo "Attempting to get all commands...\n";
    $commands = $kernel->all();
    echo "Commands loaded: " . count($commands) . "\n";
    
    // 尝试运行一个简单的命令
    echo "Attempting to run --version command...\n";
    $input = new Symfony\Component\Console\Input\ArrayInput(['--version' => true]);
    $output = new Symfony\Component\Console\Output\BufferedOutput();
    
    $status = $kernel->handle($input, $output);
    echo "Command executed with status: $status\n";
    echo "Output: " . $output->fetch() . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    
    // 获取更详细的堆栈跟踪
    $trace = $e->getTrace();
    foreach ($trace as $i => $frame) {
        if (isset($frame['file']) && isset($frame['line'])) {
            echo "#{$i} {$frame['file']}({$frame['line']})";
            if (isset($frame['function'])) {
                echo ": {$frame['function']}()";
            }
            echo "\n";
            
            // 只显示前10个堆栈帧
            if ($i >= 10) break;
        }
    }
}
