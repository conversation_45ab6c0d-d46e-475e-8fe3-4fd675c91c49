<?php

require __DIR__.'/vendor/autoload.php';

try {
    $app = require_once __DIR__.'/bootstrap/app.php';
    echo "Bootstrap successful\n";
    
    // 尝试手动注册服务提供者
    $app->register(App\Providers\AppServiceProvider::class);
    echo "AppServiceProvider registered\n";
    
    $app->register(App\Providers\AuthServiceProvider::class);
    echo "AuthServiceProvider registered\n";
    
    $app->register(App\Providers\EventServiceProvider::class);
    echo "EventServiceProvider registered\n";
    
    $app->register(App\Providers\RouteServiceProvider::class);
    echo "RouteServiceProvider registered\n";
    
    // 尝试获取控制台内核
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    echo "Kernel created successfully\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    
    // 获取更详细的堆栈跟踪
    $trace = $e->getTrace();
    foreach ($trace as $i => $frame) {
        if (isset($frame['file']) && isset($frame['line'])) {
            echo "#{$i} {$frame['file']}({$frame['line']})";
            if (isset($frame['function'])) {
                echo ": {$frame['function']}()";
            }
            echo "\n";
        }
    }
}
