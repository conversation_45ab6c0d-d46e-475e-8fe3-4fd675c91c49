{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": [{"type": "path", "url": "./packages/dujiaoka/geetest"}], "require": {"php": "^8.2", "amrshawky/laravel-currency": "^6.0", "dcat/easy-excel": "^1.0", "dcat/laravel-admin": "2.*", "dujiaoka/geetest": "*", "jenssegers/agent": "^2.6", "laravel/framework": "^10.0", "laravel/tinker": "^2.8", "mews/captcha": "^3.2", "srmklive/paypal": "^3.0", "simplesoftwareio/simple-qrcode": "^4.2", "stripe/stripe-php": "^10.0", "xhat/payjs-laravel": "^1.6", "yansongda/pay": "^3.0"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": ["barryvdh/laravel-debugbar"]}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "classmap": ["database/seeds", "database/factories"], "files": ["app/Helpers/functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}