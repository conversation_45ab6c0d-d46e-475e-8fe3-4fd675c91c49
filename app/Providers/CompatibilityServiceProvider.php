<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Filesystem\Filesystem;
use Illuminate\View\Compilers\BladeCompiler;

class CompatibilityServiceProvider extends ServiceProvider
{
    /**
     * Register services for backward compatibility with Dcat Admin and other packages.
     */
    public function register(): void
    {
        // 注册 files 服务 - Dcat Admin 需要
        $this->app->singleton('files', function ($app) {
            return new Filesystem();
        });

        // 注册 blade.compiler 服务 - Dcat Admin 需要
        $this->app->singleton('blade.compiler', function ($app) {
            $cachePath = storage_path('framework/views');
            if (!is_dir($cachePath)) {
                mkdir($cachePath, 0755, true);
            }
            return new BladeCompiler($app['files'], $cachePath);
        });

        // 注册 validator 别名 - 某些包可能需要
        $this->app->alias('validator', 'validation');

        // 注册 url 别名 - 某些包可能需要  
        $this->app->alias('url', 'url.generator');

        // 注册 html 服务 - 如果 Dcat Admin 需要
        if (!$this->app->bound('html')) {
            $this->app->singleton('html', function ($app) {
                return new \stdClass(); // 占位符，避免错误
            });
        }

        // 注册 form 服务 - 如果 Dcat Admin 需要
        if (!$this->app->bound('form')) {
            $this->app->singleton('form', function ($app) {
                return new \stdClass(); // 占位符，避免错误
            });
        }
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 确保视图缓存目录存在
        $viewCachePath = storage_path('framework/views');
        if (!is_dir($viewCachePath)) {
            mkdir($viewCachePath, 0755, true);
        }
    }
}
