<?php

namespace App\Exceptions;

use App\Exceptions\AppException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception)
    {
        if ($exception instanceof AppException) {
            $layout = dujiaoka_config_get('template', 'layui');
            $tplPath = $layout . '/errors/error';
            return view($tplPath, [
                'title' => __('dujiaoka.error_title'),
                'content' => $exception->getMessage(),
                'url' => ""
            ]);
        }
        return parent::render($request, $exception);
    }
}
